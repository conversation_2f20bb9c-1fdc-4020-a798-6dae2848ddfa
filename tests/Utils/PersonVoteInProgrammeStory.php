<?php

namespace App\Tests\Utils;

use Eduprat\DomainBundle\Factory\EvaluationGlobalAnswerFactory;
use Ed<PERSON>rat\DomainBundle\Factory\FormationFactory;
use Ed<PERSON>rat\DomainBundle\Factory\ParticipantFactory;
use Eduprat\DomainBundle\Factory\ParticipationFactory;
use Eduprat\DomainBundle\Factory\PersonFactory;
use Eduprat\DomainBundle\Factory\ProgrammeFactory;
use Zenstruck\Foundry\Proxy;
use function Zenstruck\Foundry\Persistence\flush_after;

final class PersonVoteInProgrammeStory
{
    const NB_VOTE_TWO_DAYS_AGO = 1;
    const NB_VOTE_YESTERDAY = 5;
    const VOTE_YESTERDAY = 4;
    const VOTE_TWO_DAYS_AGO = 3;
    const NB_VOTE_NULL_YESTERDAY = 2;
    const NB_VOTE_TOMORROW = 3;
    const YESTERDAY_5_VOTES_4 = 'yesterday5votes4';
    const TWODAYSAGO_1_VOTES_3 = 'twodaysago1vote3';
    const YESTERDAY_2_VOTES_NULL = 'yesterday2votesNull';
    const TOMORROW_3_VOTES_NULL = 'tomorrow3votesNull';


    public function create($story, $options = []): array
    {
        switch ($story) {
            case self::YESTERDAY_5_VOTES_4:
                return $this->buildPool('yesterday', self::NB_VOTE_YESTERDAY, self::VOTE_YESTERDAY, $options);
            case self::TWODAYSAGO_1_VOTES_3:
                return $this->buildPool('today - 2 days', self::NB_VOTE_TWO_DAYS_AGO, self::VOTE_TWO_DAYS_AGO, $options);
            case self::YESTERDAY_2_VOTES_NULL:
                return $this->buildPool('yesterday', self::NB_VOTE_NULL_YESTERDAY, null, $options);
            case self::TOMORROW_3_VOTES_NULL:
                return $this->buildPool('tomorrow', self::NB_VOTE_TOMORROW, null, $options);
        }
        return [];
    }

    /**
     * @param string $date_string
     * @param array $personsParticipantRole
     * @param int $noteVotant
     * @return array
     * @throws \Exception
     */
    public function buildPool(string $date_string, int $nbParticipant, ?int $noteVotant, $options = []): array
    {
        $personsParticipantRole = null;
        if (!isset($options['role']) || $options['role'] == 'participant') {
            $personsParticipantRole = PersonFactory::new()->forceParticipant()->createMany($nbParticipant);
        } elseif ($options['role'] == 'formateur') {
            $personsParticipantRole = PersonFactory::new()->forceFormateur()->createMany($nbParticipant);
        }

        // formation valide
        $options['programme'] = $options['programme'] ?? ProgrammeFactory::createOne();
        if (!isset($options['formation'])) {
            $options['formation'] = FormationFactory::createOne([
                'startDate' => new \DateTime($date_string),
                'programme' => $options['programme'],
            ]);
        }

        flush_after(function() use ($personsParticipantRole, $options, $noteVotant) {
            foreach ($personsParticipantRole as $person) {
                // ajout des votes
                if ($noteVotant !== null) {
                    EvaluationGlobalAnswerFactory::createOne([
                        'answer' => $noteVotant,
                        'formation' => $options['formation'],
                        'person' => $person,
                    ]);
                }
                // si les personnes créées sont des participants
                if (!isset($options['role']) || $options['role'] == 'participant') {
                    // création des participations + participant liée
                    ParticipationFactory::createOne([
                        'formation' => $options['formation'],
                        'participant' => ParticipantFactory::new([
                            'user' => $person,
                        ]),
                    ]);
                }
            }
        });
        if ($options['formation'] instanceof Proxy) {
            $options['formation'] = $options['formation']->object();
        }
        if ($options['programme'] instanceof Proxy) {
            $options['programme'] = $options['programme']->object();
        }
        return $options;
    }
}
