<?php

namespace App\Tests\unit\Eduprat\AdminBundle\Services;

use Codeception\Test\Unit;
use Doctrine\Common\Collections\ArrayCollection;
use Eduprat\AdminBundle\Services\FacturationStateManager;
use Eduprat\AuditBundle\Services\CourseManager;
use Eduprat\DomainBundle\Entity\FinanceSousMode;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\FormationElearning;
use Eduprat\DomainBundle\Entity\Participation;
use Eduprat\DomainBundle\Entity\Programme;
use Doctrine\ORM\EntityManagerInterface;

/**
 * @covers \Eduprat\AdminBundle\Services\FacturationStateManager
 */
class FacturationStateManagerTest extends Unit
{

    public function isClosingDatePassedProvider(): iterable
    {
        yield "1. Les formations dont la date de cloture est cloturé depuis moins d'1 jour ne sont pas (en attente de) facturables(tions)" => [
            false, "today 00:00:00"
        ];
        yield "2. Les formations dont la date de cloture est cloturé depuis plus d'1 jour sont (en attente de) facturables(tions)" => [
            true, "yesterday 00:00:00"
        ];
    }

    /**
     * @dataProvider isClosingDatePassedProvider
     */
    public function testIsClosingDateNotPassed($assert, $stringDate): void
    {
        $formation = new Formation();
        $formation->setClosingDate(new \DateTime($stringDate));

        $fsm = $this->getFacturationStateManager();
        self::assertEquals($assert, $fsm->isClosingDatePassed($formation));
    }

    public function hasMissingAttestationInDpcProvider(): iterable
    {
        yield "1. vérification qu'une attestation obligatoire (GIP DPC et des heures hors-ligne) est manquante dans une formation non facturée" => [
            true, true, true, false, null
        ];
    }

    /**
     * @dataProvider hasMissingAttestationInDpcProvider
     */
    public function testHasMissingAttestationInDpc($assert, $thereIsOfflineHours, $createFinanceSousModeDPC, $factured, $attestationHonneur)
    {
        $programme = new Programme();
        $programme->setThereIsOfflineHours($thereIsOfflineHours);

        $formation = new Formation();
        $formation->setProgramme($programme);

        $fsm = $this->getMockBuilder(FinanceSousMode::class)->onlyMethods(['getId'])->getMock();
        $fsm->method('getId')->willReturn(1);
        $fsm->setIdentifiant($createFinanceSousModeDPC ? 'GIP Agence Nationale du DPC - 5720' : 'FAKE NOT DPC');
        $formation->addFinanceSousMode($fsm);
        if ($factured) {
            $formation->factureFinanceSousMode($fsm);
        }
        $participation = new Participation();
        $participation->setFormation($formation);
        $participation->setFinanceSousMode($fsm);
        $formation->addParticipation($participation);
        if ($attestationHonneur) {
            $participation->setAttestationHonneur($attestationHonneur);
        }

        $fsm = $this->getFacturationStateManager();
        self::assertEquals($assert, $fsm->hasMissingAttestationInDpc($formation));
    }


    public function hasUncompletedCourseInDpcProvider(): iterable
    {
        yield "1. Une formation a une course incomplete DPC si elle a au moins une participation ayant un sous mode de 
                  financement DPC non facturé n'a pas terminé son parcours" => [
            true, [['isDpc' => true, 'nextModule' => 'fake']], false
        ];
        yield "2. Une formation n'a pas de course incomplete DPC si elle n'a pas de finance sous mode DPC" => [
            false, [['isDpc' => false, 'nextModule' => 'fake']], false
        ];
        yield "3. Une formation n'a pas de course incomplete DPC si tous ses participants ont terminé leur parcours" => [
            false, [['isDpc' => true, 'nextModule' => null]], false
        ];
        yield "4. Une formation n'a pas de course incomplete DPC si chaque sous mode est déjà facturée" => [
            false, [['isDpc' => true, 'nextModule' => 'fake']], true
        ];
    }

    /**
     * @dataProvider hasUncompletedCourseInDpcProvider
     */
    public function testHasUncompletedCourseInDpc(bool $assert, array $participations, bool $isFacturedFinanceSousMode): void
    {
        $formation = $this->getMockBuilder(Formation::class)->onlyMethods(['isFacturedFinanceSousMode'])->getMock();
        $formation->method('isFacturedFinanceSousMode')->willReturn($isFacturedFinanceSousMode);

        foreach ($participations as $p) {
            $participationMock = new Participation();
            $participationMock->setNextModule($p['nextModule']);
            $participationMock->setFormation($formation);
            $fnsm = $this->getMockBuilder(FinanceSousMode::class)->onlyMethods(['isDpc'])->getMock();
            $fnsm->method('isDpc')->willReturn($p['isDpc']);
            $participationMock->setFinanceSousMode($fnsm);
            $formation->setFinanceSousMode($fnsm);
            $formation->addParticipation($participationMock);
        }
        $fsm = $this->getFacturationStateManager();
        self::assertEquals($assert, $fsm->hasUncompletedCourseInDpc($formation));
    }

    public function hasUncompletedCoursePreInDpcProvider(): iterable
    {
        yield "1. Une formation a une première partie de course incomplete DPC si elle a au moins une participation avec une première partie incomplete
            ayant un sous mode de financement DPC non facturé n'a pas terminé son parcours" => [
            true, [['isDpc' => true,]], true, false
        ];
        yield "2. Une formation n'a pas de première partie de course incomplete DPC si elle n'a pas de finance sous mode DPC" => [
            false, [['isDpc' => false,]], true, false
        ];
        yield "3. Une formation n'a pas de première partie de course incomplete DPC si tous ses participants ont terminé leur première partie de parcours" => [
            false, [['isDpc' => true,]], false, false
        ];
        yield "4. Une formation n'a pas de première partie de course incomplete DPC si chaque sous mode est déjà facturée" => [
            false, [['isDpc' => true,]], true, true
        ];
    }

    /**
     * @dataProvider hasUncompletedCoursePreInDpcProvider
     */
    public function testHasUncompletedCoursePreInDpc($assert, array $participations, bool $isDpcCoursePreUncompleted, bool $isFacturedFinanceSousMode): void
    {
        $formation = $this->getMockBuilder(Formation::class)->onlyMethods(['isFacturedFinanceSousMode'])->getMock();
        $formation->method('isFacturedFinanceSousMode')->willReturn($isFacturedFinanceSousMode);

        foreach ($participations as $p) {
            $participation = new Participation();
            $fnsm = $this->getMockBuilder(FinanceSousMode::class)->onlyMethods(['isDpc'])->getMock();
            $fnsm->method('isDpc')->willReturn($p['isDpc']);
            $participation->setFinanceSousMode($fnsm);
            $formation->setFinanceSousMode($fnsm);
            $formation->addParticipation($participation);
        }
        $fsm = $this->getMockBuilder(FacturationStateManager::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['isDpcCoursePreUncompleted'])->getMock();
        $fsm->method('isDpcCoursePreUncompleted')->willReturn($isDpcCoursePreUncompleted);
        self::assertEquals($assert, $fsm->hasUncompletedCoursePreInDpc($formation));

    }

    public function isDpcMissingAttestationProvider(): iterable
    {
        yield "1." => [
            true, false, true, FacturationStateManager::DPC_FSM_NAME, null
        ];
    }

    /**
     * @dataProvider isDpcMissingAttestationProvider
     */
    public function testIsDpcMissingAttestation($assert, bool $isFormatPresentiel, bool $thereIsOfflineHours, $identifiant, $attestationHonneur): void
    {
        $formation = new Formation();

        $programme = $this->getMockBuilder(Programme::class)->onlyMethods(['isFormatPresentiel', 'thereIsOfflineHours'])->getMock();
        $programme->method('isFormatPresentiel')->willReturn($isFormatPresentiel);
        $programme->method('thereIsOfflineHours')->willReturn($thereIsOfflineHours);
        $programme->addFormation($formation);

        $participation = new Participation();
        $participation->setFormation($formation);
        $fnsm = $this->getMockBuilder(FinanceSousMode::class)->onlyMethods(['getIdentifiant'])->getMock();
        $fnsm->method('getIdentifiant')->willReturn($identifiant);
        $participation->setFinanceSousMode($fnsm);
        if ($attestationHonneur) {
            $participation->setAttestationHonneur($attestationHonneur);
        }

        $fsm = $this->getFacturationStateManager();
        self::assertEquals($assert, $fsm->isDpcMissingAttestation($participation));
    }

    public function isDpcCourseUncompletedProvider(): iterable
    {
        yield "true" => [
            true, true, "next_module"
        ];
        yield "2 false" => [
            false, false, "next_module"
        ];
        yield "3 false" => [
            false, true, null
        ];
    }

    /**
     * @dataProvider isDpcCourseUncompletedProvider
     */
    public function testIsDpcCourseUncompleted($assert, $isDpc, $nextModule)
    {
        $participation = new Participation();
        $formation = new Formation();
        $participation->setFormation($formation);
        $fnsm = $this->getMockBuilder(FinanceSousMode::class)->onlyMethods(['isDpc'])->getMock();
        $fnsm->method('isDpc')->willReturn($isDpc);
        $participation->setFinanceSousMode($fnsm);
        $participation->setNextModule($nextModule);
        $fsm = $this->getFacturationStateManager();
        self::assertEquals($assert, $fsm->isDpcCourseUncompleted($participation));
    }

    public function hasUncheckedFnsmDpcChargeableProvider(): iterable
    {
        yield "1. Si un sous mode de financement a l'ensemble de ces participations qui sont facturables, alors la formation est potentiellement facturable" => [
            true, false, [['financeSousModeFactures' => true, 'n1' => false, 'fnsmId' => 1], ['financeSousModeFactures' => false, 'n1' => false, 'fnsmId' => 2]]
        ];
        yield "2. Si tous les sous modes de financement sont facturés (cases à coché), alors la formation n'est facturable" => [
            false, true, [['fnsmId' => 1, 'n1' => false, 'financeSousModeFactures' => null,]]
        ];
        yield "3. Si chaque sous mode de financement a une participation qui n'est pas facturable, alors la formation n'est facturable" => [
            false, false, [['financeSousModeFactures' => false, 'n1' => false, 'fnsmId' => 1], ['financeSousModeFactures' => false, 'n1' => false, 'fnsmId' => 2]]
        ];
    }

    /**
     * @dataProvider hasUncheckedFnsmDpcChargeableProvider
     */
    public function testHasUncheckedFnsmDpcChargeable(bool $assert, bool $isFacturedFinanceSousMode, array  $participations): void
    {
        $formation = $this->getMockBuilder(Formation::class)->onlyMethods(['isFacturedFinanceSousMode'])->getMock();
        $formation->method('isFacturedFinanceSousMode')->willReturn($isFacturedFinanceSousMode);

        $arrayMockReturnParticipation = [];
        foreach ($participations as $participation) {
            $p = new Participation();
            $fnsm = $this->getMockBuilder(FinanceSousMode::class)->onlyMethods(['getId'])->getMock();
            $fnsm->method('getId')->willReturn($participation['fnsmId']);
            $p->setFinanceSousMode($fnsm);
            $formation->addParticipation($p);
            $arrayMockReturnParticipation[] = [$p, $participation['n1'], $participation['financeSousModeFactures']];
        }

        $fsm = $this->getMockBuilder(FacturationStateManager::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['isParticipationChargeable'])->getMock();
        $fsm->method('isParticipationChargeable')->willReturnMap($arrayMockReturnParticipation);

        self::assertEquals($assert, $fsm->hasUncheckedFnsmDpcChargeable($formation));
    }

    public function isParticipationChargeableProvider(): iterable
    {
        yield '1. Participation sur formation mono annuelle : la participation est facturable si la course n\'est pas incomplete et qu\'il ne manque pas d\'attestion obligatoire' =>  [
            true, false, false, false
        ];
        yield '2. Participation sur formation mono annuelle : la participation n\'est pas facturable s\'il manque une attestion obligatoire' =>  [
            false, false, false, true
        ];
        yield '3. Participation sur formation mono annuelle : la participation n\'est pas facturable si la course est incomplete' =>  [
            false, false, true, false
        ];
        yield '4. Participation sur formation mono annuelle : la participation est facturable si le parcours pré obligatoire n\'est pas incomplete et qu\'il ne manque pas d\'attestion obligatoire' =>  [
            true, true, false, null, false
        ];
        yield '5. Participation sur formation mono annuelle : la participation n\'est pas facturable s\'il manque une attestion obligatoire' =>  [
            false, true, false, null, true
        ];
        yield '6. Participation sur formation mono annuelle : la participation n\'est pas facturable si le parcours pré obligatoire est incomplete' =>  [
            false, true, true, null, false
        ];
    }

    /**
     * @dataProvider isParticipationChargeableProvider
     */
    public function testIsParticipationChargeable($assert, $pluri, $isDpcMissingAttestation, ?bool $isDpcCourseUncompleted = null, ?bool $isDpcCoursePreUncompleted = null): void
    {
        $formationMock = $this->getMockBuilder(Formation::class)->onlyMethods(['isPluriannuelle'])->getMock();
        $formationMock->method('isPluriannuelle')->willReturn($pluri);
        $fsm = $this->getMockBuilder(FacturationStateManager::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['isDpcMissingAttestation', 'isDpcCoursePreUncompleted', 'isDpcCourseUncompleted'])->getMock();
        $fsm->method('isDpcMissingAttestation')->willReturn($isDpcMissingAttestation);
        if ($pluri) {
            $fsm->method('isDpcCoursePreUncompleted')->willReturn($isDpcCoursePreUncompleted);
        } else {
            $fsm->method('isDpcCourseUncompleted')->willReturn($isDpcCourseUncompleted);
        }
        self::assertEquals($assert, $fsm->isParticipationChargeable((new Participation())->setFormation($formationMock)));
    }


    public function isDpcCoursePreUncompletedProvider(): iterable
    {
        yield "1. Une participation DPC, liée à une formation elearning, est incomplète sur année N si son module réunion (de l'étape 2) n'est pas terminé" => [true, true, false];
        yield "2. Une participation DPC, liée à une formation non elearning, est incomplète sur année N si le dernier module de l'étape 1 n'est pas terminé" => [true, false, false];
        yield "3. Une participation DPC, liée à une formation elearning, est complète sur année N si son module réunion (de l'étape 2) est terminé" => [false, true, true];
    }

    /**
     * @dataProvider isDpcCoursePreUncompletedProvider
     */
    public function testIsDpcCoursePreUncompleted($assert, bool $isElearning = true, bool $isStepCompleted = false): void
    {
        $formationVignetteAudit = $this->getMockBuilder(Formation::class)
            ->onlyMethods(['isFormVignetteAudit', 'getTopoFiles'])
            ->getMock();
        $formationVignetteAudit->method('isFormVignetteAudit')->willReturn(!$isElearning);
        $formationVignetteAudit->method('getTopoFiles')->willReturn(new ArrayCollection());
        $formation = $isElearning ? new FormationElearning() : $formationVignetteAudit;
        $p = new Programme();
        $formation->setProgramme($p);

        $participation = $this->getMockBuilder(Participation::class)
            ->onlyMethods(['isStepCompleted'])
            ->getMock();
        $participation->method('isStepCompleted')->willReturn($isStepCompleted);
        $participation->setFormation($formation);
        $formation->addParticipation($participation);
        $fsm = $this->getFacturationStateManager();

        $lastStep = last(CourseManager::COURSE_VIGNETTE_AUDIT[CourseManager::STEP1]);
        $participation->expects($this->once())
            ->method('isStepCompleted')
            ->with($this->equalTo($isElearning ? CourseManager::STEP2_REUNION_LABEL : $lastStep["id"]));

        self::assertEquals($assert, $fsm->isDpcCoursePreUncompleted($participation));
    }

    public function hasUncheckedFnsmHdpcProvider(): iterable
    {
        yield "1. Vérifie qu'il y a un sous mode de financement hors dpc non facturé dans la formation" => [true, true, false];
        yield "2. Faux s'il n'y a pas de formation avec un sous mode de financement hors DPC" => [false, false, false];
        yield "3. Faux s'il n'y a que des sous mode de financement facturé " => [false, true, true];
    }

    /**
     * @dataProvider hasUncheckedFnsmHdpcProvider
     */
    public function testHasUncheckedFnsmHdpc($assert, bool $isHorsDPC, bool $isFacturedFinanceSousMode): void
    {
        $formation = $this->getMockBuilder(Formation::class)
            ->onlyMethods(['isFacturedFinanceSousMode'])
            ->getMock();
        $formation->method('isFacturedFinanceSousMode')->willReturn($isFacturedFinanceSousMode);
        $participation = new Participation();
        $fnsm = $this->createMock(FinanceSousMode::class);
        $fnsm->method('isHorsDPC')->willReturn($isHorsDPC);
        $participation->setFinanceSousMode($fnsm);
        $formation->addParticipation($participation);

        $fsm = $this->getFacturationStateManager();
        self::assertEquals($assert, $fsm->hasUncheckedFnsmHdpc($formation));
    }

    public function testIsBilled()
    {

    }

    /**
     * @return FacturationStateManager
     */
    public function getFacturationStateManager(): FacturationStateManager
    {
        $courseManager = $this->getMockBuilder(CourseManager::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['isOldCourse', 'isBeforeElearningUpdate', 'isBeforeVideoAndToolBoxUpdate'])
            ->getMock();
        $courseManager
            ->method('isOldCourse')->willReturn(false);
        $courseManager
            ->method('isBeforeElearningUpdate')->willReturn(false);
        $courseManager
            ->method('isBeforeVideoAndToolBoxUpdate')->willReturn(false);

        $entityManager = $this->makeEmpty(EntityManagerInterface::class);

        return $this->getMockBuilder(FacturationStateManager::class)
            ->setConstructorArgs([$courseManager, $entityManager])
            ->onlyMethods([])
            ->getMock()
        ;
    }
}
