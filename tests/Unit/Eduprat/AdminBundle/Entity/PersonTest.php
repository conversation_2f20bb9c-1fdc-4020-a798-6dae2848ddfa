<?php

namespace App\Tests\Eduprat\AdminBundle\Entity;

use Codeception\Test\Unit;
use Eduprat\AdminBundle\Entity\Person;
use Eduprat\DomainBundle\Entity\Coordinator;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\FormationSedd;
use Eduprat\DomainBundle\Entity\Participation;
use Eduprat\DomainBundle\Entity\Programme;

class PersonTest extends Unit
{

    public function getCoordinatorCaSecteurByCoordinatorProvider(): iterable
    {
        $nu = 666; // ne doit pas pris en compte par le code testé
        // Formation uniannuelle
        yield "1. Formation uniannuelle avec 2 participations (price 100 et 50) rattachées à un coordinateur a un total de CA de 150 pour ce coordinateur"
            => [150, false, [['coordinateur' => 0, 'price' => 100, 'priceN1' => $nu], ['coordinateur' => 0, 'price' => 50, 'priceN1' => $nu]]];
        yield "2. Formation uniannuelle avec 2 participations (price 100 et 50) rattachées à 2 coordinateurs a un total de CA de 100 pour le premier coordinateur"
            => [100, false, [['coordinateur' => 0, 'price' => 100, 'priceN1' => $nu], ['coordinateur' => 1, 'price' => 50, 'priceN1' => $nu]]];
        yield "3. Formation SEDD uniannuelle de 7 jours de réunion avec 2 participations (price 100 et 50) rattachées à 2 coordinateurs a un total de CA de 700 pour le premier coordinateur"
            => [700, false, [['coordinateur' => 0, 'price' => 100, 'priceN1' => $nu], ['coordinateur' => 1, 'price' => 50, 'priceN1' => $nu]], true, 7];

        // Formation pluriannuelle
        yield "4. Formation pluriannuelle avec 2 participations (price N+1 100 et price N+1 50) rattachées à un coordinateur a un total de CA de 150 sur N+1 pour ce coordinateur"
            => [150, true, [['coordinateur' => 0, 'price' => $nu, 'priceN1' => 100], ['coordinateur' => 0, 'price' => $nu, 'priceN1' => 50]]];
        yield "5. Formation pluriannuelle avec 2 participations (price N+1 100 et 50) rattachées à 2 coordinateurs a un total de CA de 100 sur N+1 pour le premier coordinateur"
            => [100, true, [['coordinateur' => 0, 'price' => $nu, 'priceN1' => 100], ['coordinateur' => 1, 'price' => $nu, 'priceN1' => 50]]];
//        yield "Formation SEDD pluriannuelle avec 2 participations (price 100 et 50) rattachées à 2 coordinateurs a un total de CA de 700 pour le premier coordinateur"
//            => [700, true, [['coordinateur' => 0, 'price' => 5195, 'priceN1' => 100], ['coordinateur' => 1, 'price' => 7517, 'priceN1' => 50]], true, 7];
    }

    /**
     * @dataProvider getCoordinatorCaSecteurByCoordinatorProvider
     */
    public function testGetCoordinatorCaSecteurByCoordinator($assert, $pluriannuelle, $participations, $isSEDD = null, $nbjour = null)
    {
        $persons = [];
        $coordinateurs = [];
        $nbCoordinateur = count(array_unique(array_map(function ($elem) {
            return $elem['coordinateur'];
        }, $participations)));

        for ($i = 0; $i < $nbCoordinateur; $i++) {
            $person = new Person();
            $person->setFirstname('Nicolas');
            $person->setLastname('God');
            $coordinateur = new Coordinator();
            $coordinateur->setPerson($person);
            $person->addCoordinator($coordinateur);
            $persons[] = $person;
            $coordinateurs[] = $coordinateur;
        }

        $formation = !$isSEDD ? new Formation() : new FormationSedd();
        $dateImmutable = new \DateTimeImmutable('2023-01-01 08:00:00');
        $formation->setStartDate(\DateTime::createFromImmutable($dateImmutable));
        $formation->setOpeningDate(new \DateTime('2023-01-01 08:00:00'));
        if (!$nbjour) {
            $formation->setEndDate(new \DateTime('2023-02-01 08:00:00'));
            $formation->setClosingDate(new \DateTime('2023-02-01 08:00:00'));
        } else {
            // $njJour - 1 car le premier jour est déjà compté (formation mardi et mercredi = 2 jours mais un seul jour de différence)
            $dateFin = \DateTime::createFromImmutable($dateImmutable)->add(new \DateInterval('P'.($nbjour -1).'D'));
            $formation->setEndDate($dateFin);
            $formation->setClosingDate($dateFin);
        }
        if ($pluriannuelle) {
            $formation->setClosingDate($formation->getClosingDate()->add(new \DateInterval('P1Y')));
        }

        foreach ($coordinateurs as $coordinateur) {
            $formation->addCoordinator($coordinateur);
        }

        foreach ($participations as $participationDetails) {
            $participation = new Participation();
            $participation->setFormation($formation);
            $participation->setCoordinator($coordinateurs[$participationDetails['coordinateur']]);
            $participation->setPrice($participationDetails['price']);
            $participation->setPriceYearN1($participationDetails['priceN1']);
            $formation->addParticipation($participation);
        }

        /** @var Person[] $persons */
        self::assertEquals($assert, $persons[0]->getCoordinatorCaSecteurByCoordinator('2023', $pluriannuelle));
    }

    public function getMargeParticipationsByCoordinatorProvider(): iterable
    {
        $nu = 666; // ne doit pas être pris en compte par le code testé

        // Cas Formation uniannuelle avec réunion en N :
        // On souhaite la marge en N
        yield "1. Formation uniannuelle Elearning avec 1 participation (price 100) rattachée à un coordinateur a une marge de 80 pour ce coordinateur"
            => [80, false, true, Programme::PRESENCE_ELEARNING, null, [['coordinateur' => 0, 'price' => 100, 'priceN1' => $nu]], $nu, $nu, $nu, $nu, $nu, $nu];
        yield "2. Formation uniannuelle Classe Virtuelle avec 1 participation (price 100) rattachée à un coordinateur a une marge de 85 pour ce coordinateur"
            => [85, false, true, Programme::PRESENCE_VIRTUELLE, null, [['coordinateur' => 0, 'price' => 100, 'priceN1' => $nu]], $nu, $nu, $nu, $nu, $nu, $nu];
        yield "3. Formation uniannuelle Sur Site avec 1 participation (price 100) rattachée à un coordinateur a une marge de 40 pour ce coordinateur avec les 6 couts à 10"
            => [40, false, true, Programme::PRESENCE_SITE, null, [['coordinateur' => 0, 'price' => 100, 'priceN1' => $nu]], 10, 10, 10, 10, 10, 10];
        yield "4. Formation uniannuelle ayant un coordinateur LBI a une marge de 0 pour ce coordinateur"
            => [0, false, true, Programme::PRESENCE_SITE, null, [['coordinateur' => 0, 'price' => $nu, 'priceN1' => $nu]], $nu, $nu, $nu, $nu, $nu, $nu, true];
        // todo tester la présence d'un autre coordinateur LBI alors que je le suis pas : ma marge doit être de 0

        // Cas Formation pluriannuelle avec réunion en N :
        // On souhaite la marge en N
        yield "5. Formation pluriannuelle Elearning avec réunion sur l'année N avec 1 participation (price 100) rattachée à un coordinateur a une marge de 80 pour ce coordinateur"
            => [80, true, true, Programme::PRESENCE_ELEARNING, null, [['coordinateur' => 0, 'price' => 100, 'priceN1' => $nu]], $nu, $nu, $nu, $nu, $nu, $nu];
        yield "6. Formation pluriannuelle Classe Virtuelle avec réunion sur l'année N avec 1 participation (price 100) rattachée à un coordinateur a une marge de 85 pour ce coordinateur"
            => [85, true, true, Programme::PRESENCE_VIRTUELLE, null, [['coordinateur' => 0, 'price' => 100, 'priceN1' => $nu]], $nu, $nu, $nu, $nu, $nu, $nu];
        yield "7. Formation pluriannuelle Sur Site avec réunion sur l'année N avec 1 participation (price 100) rattachée à un coordinateur a une marge de 40 pour ce coordinateur avec les 6 couts à 10"
            => [40, true, true, Programme::PRESENCE_SITE, null, [['coordinateur' => 0, 'price' => 100, 'priceN1' => $nu]], 10, 10, 10, 10, 10, 10];

        // Cas Formation pluriannuelle avec réunion en N+1 :
        // On souhaite la marge en N
        yield "8. Formation pluriannuelle Elearning avec réunion sur l'année N+1 avec 1 participation (price 100) rattachée à un coordinateur a une marge de 80 sur l'année N pour ce coordinateur"
            => [80, true, false, Programme::PRESENCE_ELEARNING, null, [['coordinateur' => 0, 'price' => 100, 'priceN1' => $nu]], $nu, $nu, $nu, $nu, $nu, $nu];
        yield "9. Formation pluriannuelle Classe Virtuelle avec réunion sur l'année N+1 avec 1 participation (price 100) rattachée à un coordinateur a une marge de 85 sur l'année N pour ce coordinateur"
            => [85, true, false, Programme::PRESENCE_VIRTUELLE, null, [['coordinateur' => 0, 'price' => 100, 'priceN1' => $nu]], $nu, $nu, $nu, $nu, $nu, $nu];
        yield "10. Formation pluriannuelle Sur Site avec réunion sur l'année N+1 avec 1 participation (price 100 et price N+1 666) rattachées à un coordinateur a une marge de 100 sur l'année N pour ce coordinateur même si les 6 couts sont à 666 (ils sont ignorés en N)"
            => [100, true, false, Programme::PRESENCE_SITE, null, [['coordinateur' => 0, 'price' => 100, 'priceN1' => $nu]], $nu, $nu, $nu, $nu, $nu, $nu];

        // Cas Formation pluriannuelle avec réunion en N+1 :
        // On souhaite la marge en N+1
        yield "11. Formation pluriannuelle Elearning avec réunion sur l'année N+1 avec 1 participation (priceYearN1 100) rattachées à un coordinateur a une marge de 80 sur l'année N+1 pour ce coordinateur"
            => [80, true, false, Programme::PRESENCE_ELEARNING, true, [['coordinateur' => 0, 'price' => $nu, 'priceN1' => 100]], $nu, $nu, $nu, $nu, $nu, $nu];
        yield "12. Formation pluriannuelle Classe Virtuelle avec réunion sur l'année N+1 avec 1 participation (price 100) rattachée à un coordinateur a une marge de 85 sur l'année N+1 pour ce coordinateur"
            => [85, true, false, Programme::PRESENCE_VIRTUELLE, true, [['coordinateur' => 0, 'price' => $nu, 'priceN1' => 100]], $nu, $nu, $nu, $nu, $nu, $nu];
        yield "13. Formation pluriannuelle Sur Site avec réunion sur l'année N+1 avec 1 participation (price 100) rattachée à un coordinateur a une marge de 40 sur l'année N+1 pour ce coordinateur avec les 6 couts à 10"
            => [40, true, false, Programme::PRESENCE_SITE, true, [['coordinateur' => 0, 'price' => $nu, 'priceN1' => 100]], 10, 10, 10, 10, 10, 10];
    }

    /**
     * @dataProvider getMargeParticipationsByCoordinatorProvider
     */
    public function testGetMargeParticipationsByCoordinator($assert, $pluriannuelle, $dateReunionOnAnneeN, $presence, $n1, $participations,
        $costFormers = null, $costBadges = null, $costKilometres = null, $costMateriel = null,
        $costRetrocessions = null, $costDiversFormation = null, $isLBI = false
    ) {
        $persons = [];
        $coordinateurs = [];
        $nbCoordinateur = count(array_unique(array_map(function ($elem) {
            return $elem['coordinateur'];
        }, $participations)));

        for ($i = 0; $i < $nbCoordinateur; $i++) {
            $person = new Person();
            $person->setFirstname('Nicolas');
            $person->setLastname('God');
            if ($isLBI) {
                $person->setRoles([Person::ROLE_COORDINATOR_LBI]);
            }
            $coordinateur = new Coordinator();
            $coordinateur->setPerson($person);
            $coordinateur->setCostFormers($costFormers);
            $coordinateur->setCostBadges($costBadges);
            $coordinateur->setCostKilometres($costKilometres);
            $coordinateur->setCostMateriel($costMateriel);
            $coordinateur->setCostRetrocessions($costRetrocessions);
            $coordinateur->setCostDiversFormation($costDiversFormation);
            $person->addCoordinator($coordinateur);
            $persons[] = $person;
            $coordinateurs[] = $coordinateur;
        }

        $formation = new Formation();
        $dateImmutable = new \DateTimeImmutable('2023-01-01 08:00:00');
        $formation->setStartDate(\DateTime::createFromImmutable($dateImmutable));
        $formation->setOpeningDate(new \DateTime('2023-01-01 08:00:00'));
        $formation->setEndDate(new \DateTime('2023-02-01 08:00:00'));
        $formation->setClosingDate(new \DateTime('2023-02-01 08:00:00'));
        if ($pluriannuelle) {
            if (!$dateReunionOnAnneeN) {
                $formation->setStartDate($formation->getStartDate()->add(new \DateInterval('P1Y')));
                $formation->setEndDate($formation->getEndDate()->add(new \DateInterval('P1Y')));
            }
            $formation->setClosingDate($formation->getClosingDate()->add(new \DateInterval('P1Y')));
        }

        $programme = new Programme();
        $programme->setPresence($presence);
        $formation->setProgramme($programme);


        foreach ($coordinateurs as $coordinateur) {
            $formation->addCoordinator($coordinateur);
        }

        foreach ($participations as $participationDetails) {
            $participation = new Participation();
            $participation->setFormation($formation);
            $participation->setCoordinator($coordinateurs[$participationDetails['coordinateur']]);
            $participation->setPrice($participationDetails['price']);
            $participation->setPriceYearN1($participationDetails['priceN1']);
            $formation->addParticipation($participation);
        }

        /** @var Person[] $persons */
        self::assertEquals($assert, $persons[0]->getMargeParticipationsByCoordinator('2023', $n1));
    }

    public function testGetCommissionActualisee(): void
    {
        $person = new Person();
        self::assertEquals(4, $person->getCommissionActualisee(2, 2), "La commission actualisée correspond à la multiplication de la marge par le taux actualisé");
    }

    public function testCalculateCommissionExceptionnelle(): void
    {
        $person = new Person();
        self::assertEquals(1, $person->calculateCommissionExceptionnelle(4, 3), "La commission exceptionnelle correspond à la soustraction de la commission actualisée par la commission de base");
    }

    public function testGetCommissionBase(): void
    {
        $person = new Person();
        self::assertEquals(34, $person->getCommissionBase(100), "La commission de base correspond à 34% de la marge.");
    }
}
