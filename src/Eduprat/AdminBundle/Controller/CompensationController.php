<?php

namespace Eduprat\AdminBundle\Controller;

use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Eduprat\DomainBundle\Controller\EdupratController;
use Eduprat\DomainBundle\Entity\Compensation;
use Eduprat\DomainBundle\Form\CompensationType;

use Symfony\Component\HttpFoundation\Request;

use Symfony\Component\Routing\Attribute\Route;

/**
 * Class CompensationController
 */
#[Route(path: '/indemnisation')]
#[IsGranted('ROLE_WEBMASTER')]
class CompensationController extends EdupratController
{
    /**
     * @return Response
     */
    #[Route(path: '/', methods: ['GET'], name: 'admin_compensation_index')]
    public function index(EntityManagerInterface $em): Response
    {
        $compensationRepository = $em->getRepository(Compensation::class);
        $compensations = $compensationRepository->findBy(
            array(),
            array(
                'profession' => 'ASC',
                'nbHours' => 'DESC'
            )
        );

        $compensationsArray = array();
        foreach ($compensations as $key => $value) {
            $compensationsArray[$value->getProfession()][] = $value;
        }
        
        return $this->render('admin/compensation/index.html.twig', array(
            'compensations' => $compensationsArray,
        ));
    }

    /**
     * @param Request $request
     * @return RedirectResponse|Response
     */
    #[Route(path: '/create', name: 'admin_compensation_create')]
    public function create(Request $request, EntityManagerInterface $entityManager)
    {
        $profession = $request->query->get('profession');
        $compensation = new Compensation();
        $form = $this->createForm(CompensationType::class, $compensation, array(
            'profession' => $profession
        ));

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {

            $entityManager->persist($compensation);
            $entityManager->flush();

            $this->flashMessages->addSuccess('compensation.create.success');
            return $this->redirectToRoute('admin_compensation_index');
        }

        return $this->render('admin/compensation/edit.html.twig', array(
            'form' => $form,
        ));
    }

    /**
     * @param Request $request
     * @param Compensation $compensation
     * @return RedirectResponse|Response
     */
    #[Route(path: '/edit/{id}', methods: ['GET', 'POST'], name: 'admin_compensation_edit')]
    public function edit(Request $request, EntityManagerInterface $em, Compensation $compensation)
    {
        $form = $this->createForm(CompensationType::class, $compensation);
        
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $em->persist($compensation);
            $em->flush();

            $this->flashMessages->addSuccess('admin.compensation.success');
            return $this->redirectToRoute('admin_compensation_index');
        }
  
        return $this->render('admin/compensation/edit.html.twig', array(
            'form' => $form,
        ));
    }

    /**
     * Deletes a Compensation entity.
     *
     * @param Request $request
     * @param Compensation $compensation
     * @return RedirectResponse|Response
     */
    #[Route(path: '/{id}/delete', methods: ['GET', 'POST'], name: 'admin_compensation_delete')]
    public function delete(Request $request, EntityManagerInterface $em, Compensation $compensation)
    {
        $form = $this->createDeleteForm($compensation);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $em->remove($compensation);
            $em->flush();
            $this->flashMessages->addSuccess('compensation.delete.success');
            return $this->redirectToRoute('admin_compensation_index');
        }

        return $this->render('admin/compensation/delete.html.twig', array(
            'compensation' => $compensation,
            'form' => $form,
        ));
    }

    /**
     * Creates a form to delete a Compensation entity.
     *
     */
    private function createDeleteForm(Compensation $compensation): FormInterface
    {
        return $this->createFormBuilder()
            ->setAction($this->generateUrl('admin_compensation_delete', array('id' => $compensation->getId())))
            ->setMethod(Request::METHOD_POST)
            ->getForm()
            ;
    }
}
