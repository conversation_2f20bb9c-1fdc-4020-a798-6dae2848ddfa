<?php

declare(strict_types=1);
namespace Eduprat\AdminBundle\Security\TwoFactor\Provider\Email\Generator;

use <PERSON><PERSON><PERSON>\TwoFactorBundle\Mailer\AuthCodeMailerInterface;
use Scheb\TwoFactorBundle\Model\Email\TwoFactorInterface;
use Sc<PERSON>b\TwoFactorBundle\Model\PersisterInterface;
use Scheb\TwoFactorBundle\Security\TwoFactor\Provider\Email\Generator\CodeGeneratorInterface;
use function random_int;

/**
 * @final
 */
class CodeGenerator implements CodeGeneratorInterface
{
    protected $expiresAfter;

    public function __construct(
        private PersisterInterface $persister,
        private AuthCodeMailerInterface $mailer,
        private int $digits,
    ) {
        $this->expiresAfter = 'PT1H';
    }

    public function generateAndSend(TwoFactorInterface $user): void
    {
        $min = 10 ** ($this->digits - 1);
        $max = 10 ** $this->digits - 1;
        $code = $this->generateCode($min, $max);
        $user->setEmailAuthCode((string) $code);
        if (\method_exists($user, 'setEmailAuthCodeCreatedAt')) {
            $user->setEmailAuthCodeCreatedAt(new \DateTime());
        }
        $this->persister->persist($user);
        $this->mailer->sendAuthCode($user);
    }

    public function reSend(TwoFactorInterface $user): void
    {
        if (\method_exists($user, 'setEmailAuthCodeCreatedAt')) {
            $user->setEmailAuthCodeCreatedAt(new \DateTime());
        }
        $this->persister->persist($user);
        $this->mailer->sendAuthCode($user);
    }

    protected function generateCode(int $min, int $max): int
    {
        return random_int($min, $max);
    }

    public function isCodeExpired(TwoFactorInterface $user): bool
    {
        if (null === $this->expiresAfter || !\method_exists($user, 'getEmailAuthCodeCreatedAt') || !\method_exists($user, 'setEmailAuthCodeCreatedAt')) {
            return false;
        }

        $now = new \DateTime();
        $expiresAt = $user->getEmailAuthCodeCreatedAt()?->add(new \DateInterval($this->expiresAfter));

        return null !== $expiresAt && $now->getTimestamp() >= $expiresAt->getTimestamp();
    }
}
