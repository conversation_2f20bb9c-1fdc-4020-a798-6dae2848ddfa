<?php

namespace Eduprat\AuditBundle\Form;

use Eduprat\DomainBundle\Entity\Participation;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class FicheProgressionCollectionType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('ficheProgressions', CollectionType::class, array(
                'entry_type'   		=> FicheProgressionType::class,
                'prototype'			=> true,
                'allow_add'			=> false,
                'allow_delete'		=> false,
                'by_reference' 		=> false,
                'required'			=> false,
                'label'			=> false,
                'entry_options' => array(
                    'label' => false,
                    "editable" => $options["editable"]
                ),
            ))
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Participation::class,
            "editable" => true
        ]);
    }
}
