<?php

namespace Eduprat\DomainBundle\Services\Email;

use Carbon\Carbon;
use Eduprat\DomainBundle\Entity\FormationAudit;
use Eduprat\DomainBundle\Entity\FormationElearning;
use Eduprat\DomainBundle\Entity\FormationPresentielle;
use Eduprat\DomainBundle\Entity\FormationVignette;
use Eduprat\DomainBundle\Entity\FormationVignetteAudit;
use Eduprat\DomainBundle\Entity\Participation;

/**
 *
 * Audit et Présentiel => Délai : J+1 de la date de fin de réunion
 *
 * Envoyé seulement si le questionnaire post-session n'est pas déjà complété
 *
 * Class EmailFormationJP28
 * @package Eduprat\DomainBundle\Services\Email
 */
class EmailFormationJP28 extends BaseEmailParticipation
{
    const ALIAS = 'formation_jp28';
    const TEMPLATE = "domain/email/formation_jp28.html.twig";
    const NB_MAX_EMAIL_BEFORE_ALERT = 500;

    public function loadItems(): array
    {
        $startDate = Carbon::now();
        return $this->getRepository()->findByUnityOpeningDate($startDate, array(
            FormationAudit::class,
            FormationPresentielle::class,
            FormationVignette::class,
            FormationVignetteAudit::class,
            FormationElearning::class,
        ), true);
    }

    /**
     * @param Participation $item
     */
    public function shouldSend($item): bool
    {
        $startDate = Carbon::now();
        return parent::shouldSend($item) && !$this->isCompleted($item, 2) && $item->getFormation()->isFormPost28d() && $item->getFormation()->thirdUnityOpeningDate() == $startDate->setTime('0','0','0');
    }

    /**
     * @param Participation $item
     * @param               $id
     */
    public function isCompleted($item, $id): bool
    {
        return !$item->getNextModule();
    }

    /**
     * @param Participation $item
     */
    public function getSubject($item): string
    {
        $coordinator = $item && $item->getCoordinator() && $item->getCoordinator()->getPerson() ? $item->getCoordinator()->getPerson()->getFullname() . ' ' : '';
        return sprintf("%s(EDUPRAT Formations) – Accès à votre parcours post-formation", $coordinator);
    }

    /**
     * @param Participation $item
     */
    public function getViewVars($item): array
    {
        return array_merge(parent::getViewVars($item), array());
    }

    static public function getAlias(): string
    {
        return self::ALIAS;
    }

    public function getTemplate($item): string
    {
        return self::TEMPLATE;
    }
}