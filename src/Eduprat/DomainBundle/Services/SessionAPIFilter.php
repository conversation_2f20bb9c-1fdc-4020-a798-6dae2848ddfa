<?php

namespace Eduprat\DomainBundle\Services;

use Ed<PERSON>rat\DomainBundle\Entity\Formation;
use Ed<PERSON>rat\DomainBundle\Entity\FormationElearning;
use Ed<PERSON>rat\DomainBundle\Entity\FormationPresentielle;
use Eduprat\DomainBundle\Entity\Programme;
use Eduprat\DomainBundle\Entity\UnitySession;

class SessionAPIFilter
{
    /**
     * @param array<Programme> $programmes
     * @return array<Programme>
     */
    public function filter(array $programmes): array
    {
        $currentDate = new \DateTime();
        foreach ($programmes as &$programme) {
            $programme->setFormations($programme->getFormations()
                ->filter(function(Formation $formation) {
                    return $formation->getEndDate() >= ((new \DateTime())->setTime(0, 0, 0));
                })
                ->filter(function(Formation $formation) use ($programme, $currentDate) {
                    if ($programme->isElearning() && $formation instanceof FormationElearning && $formation->has1Unities()) {
                        return $this->calculateKeepSessionInAPI(1, $programme, $formation, $currentDate);
                    }
                    if ($programme->isElearning() && $formation instanceof FormationElearning && $formation->has3Unities()) {
                        return $this->calculateKeepSessionInAPI(2, $programme, $formation, $currentDate);
                    }
                    if ($programme->isSurSite() && $formation instanceof FormationElearning && $formation->has2Unities()) {
                        return $this->calculateKeepSessionInAPI(1, $programme, $formation, $currentDate);
                    }

                    $openingDate = false;
                    if ($formation->getUnityByPosition(1)) {
                        if ($programme->getUnityByPosition(1)->isOnsite()) {
                            if ($formation->getUnityByPosition(1)->getUnitySessionDates() && isset($formation->getUnityByPosition(1)->getUnitySessionDates()[0])) {
                                $openingDate = $formation->getUnityByPosition(1)->getUnitySessionDates()[0]->getStartDate();
                            }
                        } else {
                            $openingDate = $formation->getUnityByPosition(1)->getOpeningDate();
                        }
                    }
                    return !$openingDate || $openingDate >= $currentDate;
                }
            ));
        }
        return $programmes;
    }

    /**
     * @param Programme $programme
     * @param FormationPresentielle $formation
     * @param \DateTime $currentDate
     * @return bool
     */
    function calculateKeepSessionInAPI($num, Programme $programme, Formation $formation, \DateTime $currentDate): bool
    {
        /** @var UnitySession $unitySession */
        $unitySession = $formation->getUnityByPosition($num);
        if ($unitySession && $unitySession->getLastUnitySessionDate()) {
            return $unitySession->getLastUnitySessionDate()->getEndDate() >= $currentDate;
        }
        return !$unitySession || $unitySession->getClosingDate() >= $currentDate;
    }
}