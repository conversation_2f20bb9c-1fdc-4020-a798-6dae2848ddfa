<?php

namespace Eduprat\DomainBundle\Factory;

use Doctrine\ORM\EntityRepository;
use Ed<PERSON>rat\AuditBundle\Services\CourseManager;
use Eduprat\DomainBundle\Entity\Participation;
use Eduprat\DomainBundle\Repository\ParticipationRepository;
use Zenstruck\Foundry\Persistence\PersistentProxyObjectFactory;
use Zenstruck\Foundry\Persistence\ProxyRepositoryDecorator;
use Zenstruck\Foundry\Proxy;

/**
 * @extends PersistentProxyObjectFactory<Participation>
 *
 * @method        Participation|\Zenstruck\Foundry\Persistence\Proxy                              create(array|callable $attributes = [])
 * @method static Participation|Proxy                              createOne(array $attributes = [])
 * @method static Participation|Proxy                              find(object|array|mixed $criteria)
 * @method static Participation|Proxy                              findOrCreate(array $attributes)
 * @method static Participation|Proxy                              first(string $sortedField = 'id')
 * @method static Participation|Proxy                              last(string $sortedField = 'id')
 * @method static Participation|Proxy                              random(array $attributes = [])
 * @method static Participation|Proxy                              randomOrCreate(array $attributes = [])
 * @method static ParticipationRepository|ProxyRepositoryDecorator repository()
 * @method static Participation[]|Proxy[]                          all()
 * @method static Participation[]|Proxy[]                          createMany(int $number, array|callable $attributes = [])
 * @method static Participation[]|Proxy[]                          createSequence(iterable|callable $sequence)
 * @method static Participation[]|Proxy[]                          findBy(array $attributes)
 * @method static Participation[]|Proxy[]                          randomRange(int $min, int $max, array $attributes = [])
 * @method static Participation[]|Proxy[]                          randomSet(int $number, array $attributes = [])
 *
 * @phpstan-method        Participation&Proxy<Participation> create(array|callable $attributes = [])
 * @phpstan-method static Participation&Proxy<Participation> createOne(array $attributes = [])
 * @phpstan-method static Participation&Proxy<Participation> find(object|array|mixed $criteria)
 * @phpstan-method static Participation&Proxy<Participation> findOrCreate(array $attributes)
 * @phpstan-method static Participation&Proxy<Participation> first(string $sortedField = 'id')
 * @phpstan-method static Participation&Proxy<Participation> last(string $sortedField = 'id')
 * @phpstan-method static Participation&Proxy<Participation> random(array $attributes = [])
 * @phpstan-method static Participation&Proxy<Participation> randomOrCreate(array $attributes = [])
 * @phpstan-method static ProxyRepositoryDecorator<Participation, EntityRepository> repository()
 * @phpstan-method static list<Participation&Proxy<Participation>> all()
 * @phpstan-method static list<Participation&Proxy<Participation>> createMany(int $number, array|callable $attributes = [])
 * @phpstan-method static list<Participation&Proxy<Participation>> createSequence(iterable|callable $sequence)
 * @phpstan-method static list<Participation&Proxy<Participation>> findBy(array $attributes)
 * @phpstan-method static list<Participation&Proxy<Participation>> randomRange(int $min, int $max, array $attributes = [])
 * @phpstan-method static list<Participation&Proxy<Participation>> randomSet(int $number, array $attributes = [])
 */
final class ParticipationFactory extends PersistentProxyObjectFactory
{
    public function __construct()
    {
        parent::__construct();

        // TODO inject services if required (https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#factories-as-services)
    }

    protected function defaults(): array|callable
    {
        return [
            'price' => self::faker()->randomFloat(),
            'validTimeAudit1' => false,
            'validTimeAudit2' => false,
            'elearningPhaseDone' => self::faker()->boolean(),
            'nbHour' => self::faker()->randomNumber(),
            'archived' => false,
            'createdAt' => new \DateTime('today - 1 day'),
            'startedAt' => new \DateTime(),
            'completedForm1' => false,
            'completedForm2' => false,
            'completedForms' => false,
            'course' => [],
            'elearningCourse' => [],
            'next_module' => CourseManager::STEP1_VIDEO_LABEL,
            'firstPageEtutoCompleted' => false,
            'formation' => FormationFactory::new(),
            'participant' => ParticipantFactory::new(),
        ];
    }

    protected function initialize(): static
    {
        // see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#initialization
        return $this
            // ->afterInstantiate(function(Participation $participation): void {})
        ;
    }

    public static function class(): string
    {
        return Participation::class;
    }
}
