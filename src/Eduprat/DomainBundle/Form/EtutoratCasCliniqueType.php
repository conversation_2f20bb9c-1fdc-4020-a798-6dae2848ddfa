<?php

namespace Eduprat\DomainBundle\Form;

use Eduprat\DomainBundle\Entity\EtutoratCasClinique;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class EtutoratCasCliniqueType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('antecedents', null, array(
                "required" => false,
                "label" => "front.etutorat.antecedents",
                "attr" => array(
                    "rows" => 3,
                    "placeholder" => "patient de ... ans se présente à votre consultation le ... pour ..."
                )
            ))
            ->add('traitementsEnCours', null, array(
                "required" => false,
                "label" => "front.etutorat.traitementsEnCours",
                "attr" => array("rows" => 3, "placeholder" => "Saisissez votre texte ici"),
            ))
            ->add('examen', null, array(
                "required" => false,
                "label" => "front.etutorat.examen",
                "attr" => array("rows" => 3, "placeholder" => "Saisissez votre texte ici"),
            ))
            ->add('traitementAdministre', null, array(
                "required" => false,
                "label" => "front.etutorat.traitementAdministre",
                "attr" => array("rows" => 3, "placeholder" => "Saisissez votre texte ici"),
            ))
            ->add('evolution', null, array(
                "required" => false,
                "label" => "front.etutorat.evolution",
                "attr" => array("rows" => 3, "placeholder" => "Saisissez votre texte ici"),
            ))
            ->add('problematique', null, array(
                "required" => false,
                "label" => "front.etutorat.problematique",
                "attr" => array("rows" => 3, "placeholder" => "Saisissez votre texte ici"),
            ))
            ->add('questions', null, array(
                "required" => false,
                "label" => "front.etutorat.questions",
                "attr" => array("rows" => 3, "placeholder" => "Saisissez votre texte ici"),
            ))
            ->add('images', CollectionType::class, array(
                'entry_type'   		=> ImageType::class,
                'prototype'			=> true,
                'allow_add'			=> true,
                'allow_delete'		=> true,
                'by_reference' 		=> false,
                'required'			=> false,
                'label'			=> false,
                'entry_options' => array(
                    'label' => false,
                    'mimeTypes' => array('image/png', 'image/jpeg'),
                    'mimeTypesMessage' => 'formation.mimeTypesIconographie',
                ),
                'attr' => array(
                    'class' => 'images'
                ),
                'prototype_name' => '__image__',
            ))
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => EtutoratCasClinique::class,
        ]);
    }
}
