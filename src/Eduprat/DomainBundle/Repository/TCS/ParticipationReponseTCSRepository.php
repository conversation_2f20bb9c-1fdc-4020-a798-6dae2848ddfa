<?php

namespace Eduprat\DomainBundle\Repository\TCS;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Eduprat\DomainBundle\Entity\TCS\ParticipationAnswerTCS;

/**
 * @extends ServiceEntityRepository<ParticipationAnswerTCS>
 *
 * @method ParticipationAnswerTCS|null find($id, $lockMode = null, $lockVersion = null)
 * @method ParticipationAnswerTCS|null findOneBy(array $criteria, array $orderBy = null)
 * @method ParticipationAnswerTCS[]    findAll()
 * @method ParticipationAnswerTCS[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ParticipationReponseTCSRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ParticipationAnswerTCS::class);
    }

//    /**
//     * @return ParticipationReponseTCS[] Returns an array of ParticipationReponseTCS objects
//     */
//    public function findByExampleField($value): array
//    {
//        return $this->createQueryBuilder('e')
//            ->andWhere('e.exampleField = :val')
//            ->setParameter('val', $value)
//            ->orderBy('e.id', 'ASC')
//            ->setMaxResults(10)
//            ->getQuery()
//            ->getResult()
//        ;
//    }

//    public function findOneBySomeField($value): ?ParticipationReponseTCS
//    {
//        return $this->createQueryBuilder('e')
//            ->andWhere('e.exampleField = :val')
//            ->setParameter('val', $value)
//            ->getQuery()
//            ->getOneOrNullResult()
//        ;
//    }
}
