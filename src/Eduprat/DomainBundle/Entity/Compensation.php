<?php

namespace Eduprat\DomainBundle\Entity;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

/**
 * Compensation
 */
#[ORM\Entity(repositoryClass: 'Eduprat\DomainBundle\Repository\CompensationRepository')]
#[ORM\Table(name: 'compensation')]
class Compensation
{
    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: Types::INTEGER)]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    protected ?int $id = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'profession', type: Types::STRING)]
    protected ?string $profession = null;

    /**
     * @var int
     */
    #[ORM\Column(name: 'nbHours', type: Types::INTEGER)]
    protected ?int $nbHours = null;

    /**
     * @var float
     */
    #[ORM\Column(name: 'compensationParticiant', type: Types::FLOAT, nullable: true)]
    protected ?float $compensationParticiant = null;

    /**
     * @var float
     */
    #[ORM\Column(name: 'price', type: Types::FLOAT)]
    protected ?float $price = null;

    /**
     * @var float
     */
    #[ORM\Column(name: 'budgetCR', type: Types::FLOAT, nullable: true)]
    protected ?float $budgetCR = null;


    /**
     * Get id
     *
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set profession
     *
     * @param string $profession
     *
     * @return Compensation
     */
    public function setProfession($profession)
    {
        $this->profession = $profession;

        return $this;
    }
  
    /**
     * Get profession
     *
     * @return string
     */
    public function getProfession()
    {
        return $this->profession;
    }

    /**
     * Set nbHours
     *
     * @param int $nbHours
     *
     * @return Compensation
     */
    public function setNbHours($nbHours)
    {
        $this->nbHours = $nbHours;

        return $this;
    }
 
    /**
     * Get nbHours
     *
     * @return int
     */
    public function getNbHours()
    {
        return $this->nbHours;
    }

    /**
     * Set compensationParticiant
     *
     * @param float $compensationParticiant
     *
     * @return Compensation
     */
    public function setCompensationParticiant($compensationParticiant)
    {
        $this->compensationParticiant = $compensationParticiant;

        return $this;
    }
  
    /**
     * Get compensationParticiant
     *
     * @return float
     */
    public function getCompensationParticiant()
    {
        return $this->compensationParticiant;
    }

    /**
     * Set price
     *
     * @param float $price
     *
     * @return Compensation
     */
    public function setPrice($price)
    {
        $this->price = $price;

        return $this;
    }
  
    /**
     * Get price
     *
     * @return float
     */
    public function getPrice()
    {
        return $this->price;
    }

    /**
     * Set budgetCR
     *
     * @param float $budgetCR
     *
     * @return Compensation
     */
    public function setBudgetCR($budgetCR)
    {
        $this->budgetCR = $budgetCR;

        return $this;
    }
   
    /**
     * Get budgetCR
     *
     * @return float
     */
    public function getBudgetCR()
    {
        return $this->budgetCR;
    }

}