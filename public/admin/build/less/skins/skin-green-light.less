/*
 * Skin: Green
 * -----------
 */
@import "../../bootstrap-less/mixins.less";
@import "../../bootstrap-less/variables.less";
@import "../variables.less";
@import "../mixins.less";

.skin-green-light {
  //Navbar
  .main-header {
    .navbar {
      .navbar-variant(@green; #fff);
      .sidebar-toggle {
        color: #fff;
        &:hover {
          background-color: darken(@green, 5%);
        }
      }
      @media (max-width: @screen-header-collapse) {
        .dropdown-menu {
          li {
            &.divider {
              background-color: rgba(255, 255, 255, 0.1);
            }
            a {
              color: #fff;
              &:hover {
                background: darken(@green, 5%);
              }
            }
          }
        }
      }
    }
    //Logo
    .logo {
      .logo-variant(@green);
    }

    li.user-header {
      background-color: @green;
    }
  }

  //Content Header
  .content-header {
    background: transparent;
  }

  //Create the sidebar skin
  .skin-light-sidebar(@green);

}
