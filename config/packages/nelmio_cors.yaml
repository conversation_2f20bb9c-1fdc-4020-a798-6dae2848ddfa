nelmio_cors:
    defaults:
        allow_credentials: true
        allow_origin: []
        allow_headers: []
        allow_methods: []
        expose_headers: []
        max_age: 0
        hosts: []
        origin_regex: false
        forced_allow_origin_value: ~
    paths:
        '^/api/':
            allow_origin: ['*']
            allow_headers: ['Authorization', 'X-Switch-User', 'access-control-allow-headers', 'access-control-allow-method', 'access-control-allow-methods', 'access-control-allow-origin', 'content-type']
            allow_methods: ['OPTIONS', 'POST', 'PUT', 'GET', 'DELETE']
            max_age: 3600
        '^/':
            origin_regex: true
            allow_origin: ['^http://localhost:[0-9]+']
            allow_headers: []
            allow_methods: ['OPTIONS', 'POST', 'PUT', 'GET', 'DELETE']
            max_age: 3600
            hosts: ['^api\.']